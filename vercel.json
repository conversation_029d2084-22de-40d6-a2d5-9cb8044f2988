{"buildCommand": "npm run build", "installCommand": "npm install", "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "env": {"SKIP_ENV_VALIDATION": "true"}, "build": {"env": {"SKIP_ENV_VALIDATION": "true"}}, "crons": [{"path": "/api/cron/cleanup-pending-tickets?maxAge=24&includeFailedPayments=true", "schedule": "0 2 * * *"}, {"path": "/api/cron/cleanup-pending-tickets?maxAge=1&includeFailedPayments=true", "schedule": "0 3 * * 0"}]}